# Credit Rating Search Agent - Usage Guide

## How to Run and Test with Just the Power Plant Name

This guide shows you different ways to run and test the Credit Rating Search Agent with minimal input (just a power plant name).

## 🚀 Quick Start

### Method 1: Command Line Interface (CLI) - Recommended

The easiest way to test with just a power plant name:

```bash
# Activate virtual environment first
source venv/bin/activate  # or use: ./venv/bin/credit-rating-search

# Basic search with just plant name
credit-rating-search search "Adani Power Plant"

# Better results with country (recommended)
credit-rating-search search "Adani Power Plant" --country India

# Even better with location
credit-rating-search search "Adani Power Plant" --country India --location Gujarat

# Save results to file
credit-rating-search search "Tata Power Plant" --country India --output results.json
```

### Method 2: Python Script

Create a simple test script:

```python
#!/usr/bin/env python3
from dotenv import load_dotenv
load_dotenv()  # Load .env file

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from credit_rating_agent import CreditRatingSearchAgent

# Initialize the agent
agent = CreditRatingSearchAgent()

# Search with minimal input
result = agent.search_credit_rating(
    plant_name="Adani Power Plant",
    country="India"  # Optional but improves results
)

# Display results
print(f"Found {len(result.credit_rating)} agency ratings")
for rating in result.credit_rating:
    print(f"Agency: {rating.name}")
    for yearly in rating.yearwise_rating:
        print(f"  {yearly.year}: {yearly.rating}")
```

### Method 3: Integration Interface

```python
from credit_rating_agent.integration import CreditRatingIntegration

integration = CreditRatingIntegration()

# Simple search method
result = integration.search_single_plant(
    plant_name="NTPC Power Plant",
    country="India"
)

print(f"Found ratings: {len(result['credit_rating'])}")
```

## 🧪 Testing Commands

### 1. Quick Test Script
Run the provided test script:
```bash
./venv/bin/python3.12 quick_test.py
```

### 2. Example Scripts
Run comprehensive examples:
```bash
./venv/bin/python3.12 examples/basic_usage.py
```

### 3. Unit Tests
Run the test suite:
```bash
./venv/bin/pytest tests/
```

## 📋 Common Test Cases

### Indian Power Plants
```bash
credit-rating-search search "Adani Power Plant" --country India
credit-rating-search search "Tata Power Plant" --country India
credit-rating-search search "NTPC Power Plant" --country India
credit-rating-search search "Reliance Power Plant" --country India
credit-rating-search search "JSW Energy Plant" --country India
```

### International Power Plants
```bash
credit-rating-search search "Duke Energy Plant" --country "United States"
credit-rating-search search "EDF Power Plant" --country "France"
credit-rating-search search "E.ON Power Plant" --country "Germany"
```

## 🔧 Setup Requirements

### 1. Environment Setup
Make sure your `.env` file is configured:
```bash
GEMINI_API_KEY="your_api_key_here"
CONFIG_PATH=config.yaml
LOG_LEVEL=DEBUG
DEBUG=true
```

### 2. Virtual Environment
```bash
# Activate virtual environment
source venv/bin/activate

# Or use direct paths
./venv/bin/python3.12
./venv/bin/credit-rating-search
```

### 3. Package Installation
```bash
# Install in development mode
./venv/bin/pip install -e .
```

## 📊 Expected Output Format

The agent returns structured JSON data:

```json
{
  "credit_rating": [
    {
      "agency": "CRI",
      "name": "CRISIL",
      "yearwise_rating": [
        {
          "rating": "AA- Stable",
          "rating_trunc": "AA-",
          "year": "2023"
        }
      ]
    }
  ],
  "credit_rating_note": "Rating sourced from parent company financial reports",
  "currency": "INR",
  "level": "parent_company"
}
```

## 🎯 Search Strategy

The agent uses a multi-tier approach:

1. **Tier 1**: Direct power plant search
   - Searches for exact plant name with rating keywords
   - Checks multiple rating agencies (CRISIL, ICRA, CARE, etc.)

2. **Tier 2**: Corporate structure search
   - Identifies parent/holding companies
   - Maps ownership relationships

3. **Tier 3**: Parent company rating search
   - Searches for parent company ratings
   - Applies ratings to subsidiary plants

## 🌍 Geographic Coverage

### India
- **Agencies**: CRISIL, ICRA, CARE Ratings, India Ratings, Brickwork
- **Currency**: INR

### United States
- **Agencies**: Moody's, S&P Global, Fitch
- **Currency**: USD

### UK/Europe
- **Agencies**: Fitch, S&P Global, Moody's, Scope Ratings
- **Currency**: EUR/GBP

## 🚨 Troubleshooting

### Common Issues

1. **API Key Not Found**
   ```bash
   # Make sure .env file is loaded
   source .env  # or
   export GEMINI_API_KEY="your_key"
   ```

2. **CLI Command Not Found**
   ```bash
   # Install the package
   ./venv/bin/pip install -e .
   ```

3. **No Results Found**
   - Try adding country parameter
   - Check if plant name is spelled correctly
   - Try variations of the plant name

### Debug Mode
Enable debug logging:
```bash
export LOG_LEVEL=DEBUG
export DEBUG=true
```

## 📝 Example Commands for Testing

```bash
# Test with minimal input
credit-rating-search search "Adani Power"

# Test with full parameters
credit-rating-search search "Adani Power Plant" --country India --location Gujarat --output adani_results.json

# Test batch processing
echo '[{"name": "Test Plant", "country": "India"}]' > test_plants.json
credit-rating-search process test_plants.json --output results.json

# Validate results
credit-rating-search validate results.json
```

## 🎉 Success Indicators

When the agent is working correctly, you should see:
- ✅ Agent initialization messages
- ✅ Search progress logs
- ✅ Results with agency ratings
- ✅ Structured JSON output

The search process may take 1-3 minutes depending on the complexity of the search and number of results to process.