#!/usr/bin/env python3
"""
Quick test script for Credit Rating Search Agent
"""

import os
import sys
from pathlib import Path

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv()

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_basic_search():
    """Test basic search functionality"""
    print("=== Testing Basic Search ===")
    
    try:
        from credit_rating_agent import CreditRatingSearchAgent
        
        # Initialize agent
        agent = CreditRatingSearchAgent()
        print("✓ Agent initialized successfully")
        
        # Test search with minimal parameters
        print("Searching for 'Adani Power Plant'...")
        result = agent.search_credit_rating(
            plant_name="Adani Power Plant",
            country="India"
        )
        
        print(f"✓ Search completed")
        print(f"  - Found {len(result.credit_rating)} agency ratings")
        print(f"  - Currency: {result.currency}")
        print(f"  - Level: {result.level}")
        print(f"  - Note: {result.credit_rating_note}")
        
        if result.credit_rating:
            print("  - Rating details:")
            for rating in result.credit_rating:
                print(f"    * {rating.name} ({rating.agency})")
                for yearly in rating.yearwise_rating:
                    print(f"      - {yearly.year}: {yearly.rating}")
        
        return True
        
    except Exception as e:
        print(f"✗ Search failed: {e}")
        return False

def test_integration_interface():
    """Test integration interface"""
    print("\n=== Testing Integration Interface ===")
    
    try:
        from credit_rating_agent.integration import CreditRatingIntegration
        
        integration = CreditRatingIntegration()
        print("✓ Integration initialized successfully")
        
        # Test simple search
        result = integration.search_single_plant(
            plant_name="Tata Power Plant",
            country="India"
        )
        
        print(f"✓ Integration search completed")
        print(f"  - Found {len(result['credit_rating'])} ratings")
        print(f"  - Currency: {result['currency']}")
        
        return True
        
    except Exception as e:
        print(f"✗ Integration test failed: {e}")
        return False

def test_cli_availability():
    """Test if CLI is available"""
    print("\n=== Testing CLI Availability ===")
    
    try:
        import subprocess
        result = subprocess.run(
            ["credit-rating-search", "--version"], 
            capture_output=True, 
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("✓ CLI command available")
            print(f"  - Version: {result.stdout.strip()}")
            return True
        else:
            print("✗ CLI command failed")
            print(f"  - Error: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("✗ CLI command not found")
        print("  - Try running: pip install -e .")
        return False
    except Exception as e:
        print(f"✗ CLI test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Credit Rating Search Agent - Quick Test")
    print("=" * 50)
    
    # Check API key
    if not os.getenv('GEMINI_API_KEY'):
        print("⚠️  WARNING: GEMINI_API_KEY not found in environment")
        print("   Make sure your .env file is loaded")
        return
    else:
        print("✓ GEMINI_API_KEY found")
    
    # Run tests
    tests = [
        test_basic_search,
        test_integration_interface,
        test_cli_availability
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    # Summary
    print("\n" + "=" * 50)
    passed = sum(results)
    total = len(results)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! The agent is ready to use.")
        print("\nQuick usage examples:")
        print("  CLI: credit-rating-search search 'Plant Name' --country India")
        print("  Python: agent.search_credit_rating(plant_name='Plant Name', country='India')")
    else:
        print("❌ Some tests failed. Check the errors above.")

if __name__ == "__main__":
    main()