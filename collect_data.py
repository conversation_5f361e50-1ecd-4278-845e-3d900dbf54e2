#!/usr/bin/env python3
"""
Data Collection Script for Power Plant Credit Ratings
"""

import os
import json
import sys
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from credit_rating_agent import CreditRatingSearchAgent
from credit_rating_agent.integration import CreditRatingIntegration

def collect_single_plant_data(plant_name, country="India", location=None):
    """Collect data for a single power plant"""
    print(f"🔍 Collecting data for: {plant_name}")
    
    try:
        agent = CreditRatingSearchAgent()
        
        result = agent.search_credit_rating(
            plant_name=plant_name,
            country=country,
            location=location
        )
        
        # Convert to dictionary for easier handling
        result_dict = {
            "plant_name": plant_name,
            "country": country,
            "location": location,
            "credit_rating": [
                {
                    "agency": rating.agency,
                    "name": rating.name,
                    "yearwise_rating": [
                        {
                            "year": yearly.year,
                            "rating": yearly.rating,
                            "rating_trunc": yearly.rating_trunc
                        } for yearly in rating.yearwise_rating
                    ]
                } for rating in result.credit_rating
            ],
            "credit_rating_note": result.credit_rating_note,
            "currency": result.currency,
            "level": result.level
        }
        
        print(f"✅ Found {len(result.credit_rating)} agency ratings")
        return result_dict
        
    except Exception as e:
        print(f"❌ Error collecting data for {plant_name}: {e}")
        return None

def collect_multiple_plants_data(plants_list):
    """Collect data for multiple power plants"""
    results = []
    
    for i, plant_info in enumerate(plants_list, 1):
        print(f"\n📊 Processing {i}/{len(plants_list)}")
        
        if isinstance(plant_info, str):
            # Simple plant name
            result = collect_single_plant_data(plant_info)
        else:
            # Dictionary with plant details
            result = collect_single_plant_data(
                plant_name=plant_info.get("name"),
                country=plant_info.get("country", "India"),
                location=plant_info.get("location")
            )
        
        if result:
            results.append(result)
    
    return results

def save_results(results, filename="power_plant_ratings.json"):
    """Save results to JSON file"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"💾 Results saved to: {filename}")
        return True
    except Exception as e:
        print(f"❌ Error saving results: {e}")
        return False

def main():
    """Main data collection function"""
    print("🏭 Power Plant Credit Rating Data Collection")
    print("=" * 50)
    
    # Check API key
    if not os.getenv('GEMINI_API_KEY'):
        print("❌ GEMINI_API_KEY not found. Please check your .env file.")
        return
    
    # Define power plants to collect data for
    power_plants = [
        # Indian Power Plants
        {
            "name": "Adani Power Plant",
            "country": "India",
            "location": "Gujarat"
        },
        {
            "name": "Tata Power Plant", 
            "country": "India",
            "location": "Maharashtra"
        },
        {
            "name": "NTPC Power Plant",
            "country": "India",
            "location": "Uttar Pradesh"
        },
        {
            "name": "Reliance Power Plant",
            "country": "India",
            "location": "Gujarat"
        },
        {
            "name": "JSW Energy Plant",
            "country": "India",
            "location": "Karnataka"
        },
        # Add more plants as needed
        "BHEL Power Plant",  # Simple string format
        "Essar Power Plant",
    ]
    
    print(f"📋 Collecting data for {len(power_plants)} power plants...")
    
    # Collect data
    results = collect_multiple_plants_data(power_plants)
    
    # Save results
    if results:
        save_results(results, "collected_power_plant_data.json")
        
        # Print summary
        print(f"\n📈 Collection Summary:")
        print(f"   Total plants processed: {len(power_plants)}")
        print(f"   Successful collections: {len(results)}")
        print(f"   Failed collections: {len(power_plants) - len(results)}")
        
        # Show sample results
        for result in results[:3]:  # Show first 3 results
            ratings_count = len(result['credit_rating'])
            print(f"   • {result['plant_name']}: {ratings_count} agency ratings")
    
    else:
        print("❌ No data collected successfully")

if __name__ == "__main__":
    main()