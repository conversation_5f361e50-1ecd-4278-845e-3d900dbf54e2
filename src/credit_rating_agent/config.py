"""
Configuration management for Credit Rating Search Agent
"""

import os
import yaml
from typing import Dict, List, Any, Optional
from pathlib import Path
from dotenv import load_dotenv
import logging

from .models import RatingAgency


class Config:
    """Configuration manager for the Credit Rating Search Agent"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize configuration

        Args:
            config_path: Path to configuration file (defaults to config.yaml)
        """
        # Load environment variables from .env file
        # Look for .env in current directory and parent directories
        load_dotenv(dotenv_path='.env', verbose=True)

        # Also try to load from project root if we're in a subdirectory
        project_root = Path(__file__).parent.parent.parent
        env_file = project_root / '.env'
        if env_file.exists():
            load_dotenv(dotenv_path=env_file, verbose=True)
        
        # Set default config path
        if config_path is None:
            config_path = os.getenv('CONFIG_PATH', 'config.yaml')
        
        self.config_path = Path(config_path)
        self._config = self._load_config()
        self._setup_logging()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        try:
            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            # Substitute environment variables
            config = self._substitute_env_vars(config)
            return config
            
        except FileNotFoundError:
            logging.warning(f"Config file {self.config_path} not found, using defaults")
            return self._get_default_config()
        except yaml.YAMLError as e:
            logging.error(f"Error parsing config file: {e}")
            return self._get_default_config()
    
    def _substitute_env_vars(self, obj: Any) -> Any:
        """Recursively substitute environment variables in config"""
        if isinstance(obj, dict):
            return {k: self._substitute_env_vars(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._substitute_env_vars(item) for item in obj]
        elif isinstance(obj, str) and obj.startswith('${') and obj.endswith('}'):
            env_var = obj[2:-1]
            return os.getenv(env_var, obj)
        else:
            return obj
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'gemini': {
                'api_key': os.getenv('GEMINI_API_KEY'),
                'model': 'gemini-1.5-pro',
                'temperature': 0.1,
                'max_tokens': 8192,
                'timeout': 30
            },
            'search': {
                'max_results_per_query': 10,
                'timeout_seconds': 30,
                'retry_attempts': 3,
                'backoff_factor': 2,
                'rate_limit_delay': 1.0,
                'tiers': {
                    'tier1_direct_search': True,
                    'tier2_corporate_search': True,
                    'tier3_parent_company_search': True
                },
                'rating_years': {
                    'start_year': 2019,
                    'end_year': 2024
                }
            },
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                'file': 'credit_rating_search.log'
            }
        }
    
    def _setup_logging(self):
        """Setup logging configuration"""
        log_config = self._config.get('logging', {})
        level = getattr(logging, log_config.get('level', 'INFO').upper())
        format_str = log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        
        # Configure root logger
        logging.basicConfig(
            level=level,
            format=format_str,
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(log_config.get('file', 'credit_rating_search.log'))
            ]
        )
    
    @property
    def gemini_api_key(self) -> str:
        """Get Gemini API key"""
        api_key = self._config.get('gemini', {}).get('api_key')
        if not api_key:
            raise ValueError("GEMINI_API_KEY not found in environment or config")
        return api_key
    
    @property
    def gemini_config(self) -> Dict[str, Any]:
        """Get Gemini API configuration"""
        return self._config.get('gemini', {})
    
    @property
    def search_config(self) -> Dict[str, Any]:
        """Get search configuration"""
        return self._config.get('search', {})
    
    @property
    def rating_years(self) -> Dict[str, int]:
        """Get rating years configuration"""
        return self._config.get('search', {}).get('rating_years', {
            'start_year': 2019,
            'end_year': 2024
        })
    
    def get_rating_agencies(self, country: str) -> List[RatingAgency]:
        """
        Get rating agencies for a specific country
        
        Args:
            country: Country name or code
            
        Returns:
            List of RatingAgency objects for the country
        """
        country_lower = country.lower()
        agencies_config = self._config.get('rating_agencies', {})
        
        # Map country to config key
        country_mapping = {
            'india': 'india',
            'in': 'india',
            'united states': 'united_states',
            'usa': 'united_states',
            'us': 'united_states',
            'united kingdom': 'united_kingdom',
            'uk': 'united_kingdom',
            'gb': 'united_kingdom'
        }
        
        config_key = country_mapping.get(country_lower)
        if not config_key:
            # Default to US agencies for unknown countries
            config_key = 'united_states'
        
        agencies = []
        country_config = agencies_config.get(config_key, {})
        
        # Add primary agencies
        for agency_config in country_config.get('primary', []):
            agencies.append(RatingAgency(
                code=agency_config['code'],
                name=agency_config['name'],
                currency=agency_config['currency'],
                region=config_key,
                priority='primary'
            ))
        
        # Add secondary agencies
        for agency_config in country_config.get('secondary', []):
            agencies.append(RatingAgency(
                code=agency_config['code'],
                name=agency_config['name'],
                currency=agency_config['currency'],
                region=config_key,
                priority='secondary'
            ))
        
        # Add regional agencies
        for agency_config in country_config.get('regional', []):
            agencies.append(RatingAgency(
                code=agency_config['code'],
                name=agency_config['name'],
                currency=agency_config['currency'],
                region=config_key,
                priority='regional'
            ))
        
        return agencies
    
    def get_outlook_indicators(self) -> List[str]:
        """Get list of outlook indicators to remove for rating truncation"""
        return self._config.get('rating_processing', {}).get('outlook_indicators', [
            "Stable", "Positive", "Negative", "Watch", "Under Review", 
            "CreditWatch", "Developing", "Evolving"
        ])
    
    def get_valid_ratings(self, agency_type: str) -> List[str]:
        """
        Get valid rating scales for agency type
        
        Args:
            agency_type: 'moodys' or 'sp_fitch'
            
        Returns:
            List of valid rating strings
        """
        return self._config.get('rating_processing', {}).get('valid_ratings', {}).get(agency_type, [])
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key"""
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
