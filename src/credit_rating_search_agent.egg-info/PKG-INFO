Metadata-Version: 2.4
Name: credit-rating-search-agent
Version: 1.0.0
Summary: A comprehensive web search agent that uses the Gemini API to automatically find and extract credit ratings for power plants worldwide
Home-page: https://github.com/your-org/credit-rating-search-agent
Author: Credit Rating Search Team
Author-email: <EMAIL>
Project-URL: Documentation, https://github.com/your-org/credit-rating-search-agent/docs
Project-URL: Source, https://github.com/your-org/credit-rating-search-agent
Project-URL: Tracker, https://github.com/your-org/credit-rating-search-agent/issues
Keywords: credit rating,power plants,financial data,web scraping,gemini api,data extraction,rating agencies,financial analysis
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Financial and Insurance Industry
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Topic :: Office/Business :: Financial
Classifier: Topic :: Scientific/Engineering :: Information Analysis
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Requires-Dist: google-generativeai>=0.3.0
Requires-Dist: requests>=2.31.0
Requires-Dist: beautifulsoup4>=4.12.0
Requires-Dist: lxml>=4.9.0
Requires-Dist: selenium>=4.15.0
Requires-Dist: webdriver-manager>=4.0.0
Requires-Dist: pandas>=2.0.0
Requires-Dist: numpy>=1.24.0
Requires-Dist: pydantic>=2.0.0
Requires-Dist: jsonschema>=4.17.0
Requires-Dist: aiohttp>=3.8.0
Requires-Dist: asyncio-throttle>=1.0.0
Requires-Dist: structlog>=23.1.0
Requires-Dist: python-json-logger>=2.0.0
Requires-Dist: python-dotenv>=1.0.0
Requires-Dist: pyyaml>=6.0
Requires-Dist: pytest>=7.4.0
Requires-Dist: pytest-asyncio>=0.21.0
Requires-Dist: pytest-mock>=3.11.0
Requires-Dist: responses>=0.23.0
Requires-Dist: black>=23.0.0
Requires-Dist: flake8>=6.0.0
Requires-Dist: mypy>=1.5.0
Provides-Extra: dev
Requires-Dist: pytest>=7.4.0; extra == "dev"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "dev"
Requires-Dist: pytest-mock>=3.11.0; extra == "dev"
Requires-Dist: pytest-cov>=4.1.0; extra == "dev"
Requires-Dist: black>=23.0.0; extra == "dev"
Requires-Dist: flake8>=6.0.0; extra == "dev"
Requires-Dist: mypy>=1.5.0; extra == "dev"
Provides-Extra: test
Requires-Dist: pytest>=7.4.0; extra == "test"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "test"
Requires-Dist: pytest-mock>=3.11.0; extra == "test"
Requires-Dist: responses>=0.23.0; extra == "test"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: keywords
Dynamic: project-url
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

# Credit Rating Search Agent

A comprehensive web search agent that uses the Gemini API to automatically find and extract credit ratings for power plants worldwide. This agent serves as a fallback mechanism that triggers when credit rating data is not available in existing JSON datasets.

## Features

- **Multi-Tier Search Strategy**: Progressive search from direct plant ratings to parent company ratings
- **Geographic-Specific Targeting**: Specialized search for rating agencies by region (India, US, UK/Europe)
- **Comprehensive Data Extraction**: 5-year temporal coverage (2019-2024) with multiple rating types
- **Standardized Output**: Consistent JSON schema with proper validation and formatting
- **Robust Error Handling**: Exponential backoff, timeout handling, and fallback strategies
- **Integration Ready**: Modular design for seamless integration into existing systems

## Quick Start

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd Credit_rating_Search
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env and add your GEMINI_API_KEY
```

### Basic Usage

```python
from credit_rating_agent import CreditRatingSearchAgent

# Initialize the agent
agent = CreditRatingSearchAgent()

# Search for credit ratings
result = agent.search_credit_rating(
    plant_name="Adani Power Plant",
    location="India",
    country="India"
)

print(result)
```

## Architecture

### Multi-Tier Search Strategy

1. **Tier 1 - Direct Power Plant Search**
   - Exact name searches with credit rating keywords
   - Financial document variations
   - Location-specific searches

2. **Tier 2 - Corporate Structure Search**
   - Ownership structure identification
   - Parent/holding company discovery
   - Subsidiary relationship mapping

3. **Tier 3 - Parent Company Rating Search**
   - Parent company credit rating searches
   - Rating agency specific queries
   - Corporate bond rating searches

### Geographic Rating Agency Coverage

- **India**: CRISIL, ICRA, CARE Ratings, India Ratings, Brickwork
- **United States**: Moody's, S&P Global, Fitch
- **UK/Europe**: Fitch, S&P Global, Moody's, Scope Ratings
- **Other Regions**: Dynamic agency identification

## Configuration

The agent uses a YAML configuration file (`config.yaml`) for customization:

- Search parameters and timeouts
- Rating agency definitions by region
- Data processing rules
- Output formatting options

## Integration

### Trigger Mechanism

The agent automatically activates when existing JSON data lacks credit rating information:

```python
# Example integration
if not has_credit_rating_data(plant_data):
    rating_data = agent.search_credit_rating(
        plant_name=plant_data['name'],
        location=plant_data['location'],
        country=plant_data['country']
    )
    plant_data.update(rating_data)
```

### Output Schema

```json
{
  "credit_rating": [
    {
      "agency": "CRI",
      "name": "CRISIL",
      "yearwise_rating": [
        {
          "rating": "AA- Stable",
          "rating_trunc": "AA-",
          "year": "2023"
        }
      ]
    }
  ],
  "credit_rating_note": "Rating sourced from parent company financial reports",
  "currency": "INR",
  "level": "parent_company"
}
```

## Development

### Running Tests

```bash
pytest tests/
```

### Code Formatting

```bash
black .
flake8 .
mypy .
```

## License

[Add your license information here]

## Contributing

[Add contribution guidelines here]
