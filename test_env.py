#!/usr/bin/env python3
"""
Test script to verify .env file loading
"""

import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from dotenv import load_dotenv

def test_env_loading():
    """Test different ways of loading .env"""
    print("Testing .env file loading...")
    print(f"Current working directory: {os.getcwd()}")
    
    # Method 1: Default load_dotenv()
    print("\n1. Testing default load_dotenv()...")
    load_dotenv(verbose=True)
    api_key_1 = os.getenv('GEMINI_API_KEY')
    print(f"   GEMINI_API_KEY found: {'Yes' if api_key_1 else 'No'}")
    
    # Method 2: Explicit path
    print("\n2. Testing explicit .env path...")
    env_file = Path('.env')
    print(f"   .env file exists: {env_file.exists()}")
    if env_file.exists():
        load_dotenv(dotenv_path=env_file, verbose=True)
        api_key_2 = os.getenv('GEMINI_API_KEY')
        print(f"   GEMINI_API_KEY found: {'Yes' if api_key_2 else 'No'}")
    
    # Method 3: Check .env content
    print("\n3. Checking .env file content...")
    if env_file.exists():
        with open(env_file, 'r') as f:
            content = f.read()
            print(f"   .env file content preview:")
            for line in content.split('\n')[:5]:  # First 5 lines
                if line.strip():
                    # Hide actual API key value
                    if 'GEMINI_API_KEY' in line:
                        parts = line.split('=', 1)
                        if len(parts) == 2:
                            print(f"   {parts[0]}=***hidden***")
                        else:
                            print(f"   {line}")
                    else:
                        print(f"   {line}")
    
    # Final check
    print(f"\n4. Final environment check:")
    final_api_key = os.getenv('GEMINI_API_KEY')
    if final_api_key:
        print(f"   ✅ GEMINI_API_KEY is loaded (length: {len(final_api_key)})")
    else:
        print(f"   ❌ GEMINI_API_KEY is not found")
        
        # List all environment variables starting with GEMINI
        gemini_vars = {k: v for k, v in os.environ.items() if 'GEMINI' in k.upper()}
        if gemini_vars:
            print(f"   Found GEMINI-related vars: {list(gemini_vars.keys())}")
        else:
            print(f"   No GEMINI-related environment variables found")

if __name__ == "__main__":
    test_env_loading()
