#!/usr/bin/env python3
"""
Interactive Credit Rating Search Agent
Run this script to interactively search for credit ratings of power plants
"""

import os
import sys
import json
from pathlib import Path
from datetime import datetime

# Load environment variables FIRST, before importing anything else
from dotenv import load_dotenv
load_dotenv(verbose=True)

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from credit_rating_agent import CreditRatingSearchAgent
    from credit_rating_agent.integration import CreditRatingIntegration
    from credit_rating_agent.output_formatter import OutputFormatter
except ImportError as e:
    print(f"Error importing credit rating agent: {e}")
    print("Make sure you have installed the dependencies: pip install -r requirements.txt")
    sys.exit(1)


def check_api_key():
    """Check if API key is available"""
    # Force reload environment variables
    load_dotenv(verbose=True, override=True)

    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("⚠️  WARNING: GEMINI_API_KEY not found in environment variables")
        print("Please set your Gemini API key in the .env file:")
        print("GEMINI_API_KEY=your_api_key_here")
        print()

        # Check if .env file exists
        env_file = Path('.env')
        if env_file.exists():
            print(f"✅ .env file found at: {env_file.absolute()}")
            print("Checking .env file content...")
            try:
                with open(env_file, 'r') as f:
                    content = f.read()
                    if 'GEMINI_API_KEY' in content:
                        print("✅ GEMINI_API_KEY found in .env file")
                        print("❌ But not loaded into environment - there might be a format issue")
                        print("Make sure your .env file has no spaces around the = sign:")
                        print("GEMINI_API_KEY=your_key_here")
                    else:
                        print("❌ GEMINI_API_KEY not found in .env file")
            except Exception as e:
                print(f"❌ Error reading .env file: {e}")
        else:
            print(f"❌ .env file not found at: {env_file.absolute()}")

        choice = input("\nContinue anyway? (y/n): ").lower().strip()
        if choice != 'y':
            print("Exiting...")
            return False
    else:
        print(f"✅ API key found (length: {len(api_key)})")
    return True


def get_plant_input():
    """Get plant information from user"""
    print("\n" + "="*60)
    print("Enter Power Plant Information")
    print("="*60)
    
    plant_name = input("Plant name: ").strip()
    if not plant_name:
        return None
    
    country = input("Country (default: India): ").strip() or "India"
    location = input("Location/State (optional): ").strip() or None
    
    return {
        'plant_name': plant_name,
        'country': country,
        'location': location
    }


def save_results(result, plant_name):
    """Save results to JSON file"""
    # Create safe filename
    safe_name = "".join(c for c in plant_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
    safe_name = safe_name.replace(' ', '_').lower()
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{safe_name}_{timestamp}.json"
    
    try:
        # Create results directory if it doesn't exist
        results_dir = Path("results")
        results_dir.mkdir(exist_ok=True)
        
        filepath = results_dir / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Results saved to: {filepath}")
        return str(filepath)
    except Exception as e:
        print(f"❌ Error saving results: {e}")
        return None


def display_results(result):
    """Display results summary"""
    print("\n" + "="*60)
    print("SEARCH RESULTS")
    print("="*60)
    
    credit_ratings = result.get('credit_rating', [])
    
    if not credit_ratings:
        print("❌ No credit ratings found")
        print(f"Note: {result.get('credit_rating_note', 'No additional information')}")
        return
    
    print(f"✅ Found ratings from {len(credit_ratings)} agencies")
    print(f"Currency: {result.get('currency', 'Unknown')}")
    print(f"Level: {result.get('level', 'Unknown')}")
    print(f"Note: {result.get('credit_rating_note', '')}")
    
    print("\nDetailed Ratings:")
    print("-" * 40)
    
    for agency in credit_ratings:
        print(f"\n🏛️  {agency['name']} ({agency['agency']})")
        
        for rating in agency.get('yearwise_rating', []):
            year = rating.get('year', 'Unknown')
            full_rating = rating.get('rating', 'Unknown')
            core_rating = rating.get('rating_trunc', 'Unknown')
            print(f"   {year}: {full_rating} (Core: {core_rating})")


def search_single_plant(agent, formatter):
    """Search for a single plant"""
    plant_info = get_plant_input()
    if not plant_info:
        print("❌ Invalid plant information")
        return False
    
    print(f"\n🔍 Searching for: {plant_info['plant_name']}")
    print("This may take a few moments...")
    
    try:
        # Perform search
        result = agent.search_credit_rating(
            plant_name=plant_info['plant_name'],
            location=plant_info['location'],
            country=plant_info['country']
        )
        
        # Format result
        formatted_result = formatter.format_result(result)
        
        # Display results
        display_results(formatted_result)
        
        # Save results
        save_results(formatted_result, plant_info['plant_name'])
        
        return True
        
    except Exception as e:
        print(f"❌ Search failed: {e}")
        return False


def batch_mode(integration):
    """Batch processing mode"""
    print("\n" + "="*60)
    print("BATCH PROCESSING MODE")
    print("="*60)
    
    input_file = input("Enter path to JSON file with plant data: ").strip()
    
    if not input_file or not os.path.exists(input_file):
        print("❌ File not found")
        return
    
    output_file = input("Enter output file path (press Enter for auto-generated): ").strip()
    if not output_file:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"batch_results_{timestamp}.json"
    
    print(f"\n🔍 Processing file: {input_file}")
    print("This may take several minutes...")
    
    try:
        success = integration.process_json_file(
            input_file_path=input_file,
            output_file_path=output_file,
            force_search=False
        )
        
        if success:
            print(f"✅ Batch processing completed!")
            print(f"Results saved to: {output_file}")
        else:
            print("❌ Batch processing failed")
            
    except Exception as e:
        print(f"❌ Batch processing error: {e}")


def main():
    """Main interactive loop"""
    print("🔍 Credit Rating Search Agent - Interactive Mode")
    print("=" * 60)
    
    # Check API key
    if not check_api_key():
        return
    
    # Initialize agent
    try:
        print("\n🚀 Initializing search agent...")
        agent = CreditRatingSearchAgent()
        integration = CreditRatingIntegration()
        formatter = OutputFormatter(agent.config)
        print("✅ Agent initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize agent: {e}")
        return
    
    # Main loop
    while True:
        print("\n" + "="*60)
        print("MAIN MENU")
        print("="*60)
        print("1. Search single power plant")
        print("2. Batch process JSON file")
        print("3. View recent results")
        print("4. Exit")
        
        choice = input("\nSelect option (1-4): ").strip()
        
        if choice == '1':
            search_single_plant(agent, formatter)
            
        elif choice == '2':
            batch_mode(integration)
            
        elif choice == '3':
            # Show recent results
            results_dir = Path("results")
            if results_dir.exists():
                json_files = list(results_dir.glob("*.json"))
                if json_files:
                    print(f"\n📁 Found {len(json_files)} recent result files:")
                    for i, file in enumerate(sorted(json_files, key=lambda x: x.stat().st_mtime, reverse=True)[:10]):
                        print(f"   {i+1}. {file.name}")
                else:
                    print("\n📁 No result files found")
            else:
                print("\n📁 No results directory found")
                
        elif choice == '4':
            print("\n👋 Thank you for using Credit Rating Search Agent!")
            break
            
        else:
            print("❌ Invalid choice. Please select 1-4.")
        
        # Ask if user wants to continue
        if choice in ['1', '2']:
            continue_choice = input("\nSearch another plant? (y/n): ").lower().strip()
            if continue_choice != 'y':
                print("\n👋 Thank you for using Credit Rating Search Agent!")
                break


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
