import sys
from asyncio.events import Abstract<PERSON><PERSON><PERSON>oop
from collections.abc import Awaitable, Callable
from typing import <PERSON><PERSON><PERSON>
from typing_extensions import ParamSpec

from .case import TestCase

if sys.version_info >= (3, 11):
    from contextlib import AbstractAsyncContextManager

_T = TypeVar("_T")
_P = ParamSpec("_P")

class IsolatedAsyncioTestCase(TestCase):
    if sys.version_info >= (3, 13):
        loop_factory: Callable[[], AbstractEventLoop] | None = None

    async def asyncSetUp(self) -> None: ...
    async def asyncTearDown(self) -> None: ...
    def addAsyncCleanup(self, func: Callable[_P, Awaitable[object]], /, *args: _P.args, **kwargs: _P.kwargs) -> None: ...
    if sys.version_info >= (3, 11):
        async def enterAsyncContext(self, cm: AbstractAsyncContextManager[_T]) -> _T: ...

    def __del__(self) -> None: ...
