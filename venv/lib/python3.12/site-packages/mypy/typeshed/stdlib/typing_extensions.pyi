import abc
import enum
import sys
from _collections_abc import dict_items, dict_keys, dict_values
from _typeshed import Annota<PERSON>Form, IdentityFunction, Incomplete, Unused
from collections.abc import (
    AsyncGenerator as AsyncGenerator,
    AsyncIterable as AsyncIterable,
    AsyncIterator as AsyncIterator,
    Awaitable as Awaitable,
    Collection as Collection,
    Container as Container,
    Coroutine as Coroutine,
    Generator as Generator,
    Hashable as Hashable,
    ItemsView as ItemsView,
    Iterable as Iterable,
    Iterator as Iterator,
    KeysView as KeysView,
    Mapping as Mapping,
    MappingView as MappingView,
    MutableMapping as MutableMapping,
    MutableSequence as MutableSequence,
    MutableSet as MutableSet,
    Reversible as Reversible,
    Sequence as Sequence,
    Sized as Sized,
    ValuesView as ValuesView,
)
from contextlib import AbstractAsyncContextManager as AsyncContextManager, AbstractContextManager as ContextManager
from re import Match as Match, Pattern as Pattern
from types import <PERSON>ric<PERSON><PERSON><PERSON>, ModuleType
from typing import (  # noqa: Y022,Y037,Y038,Y039,UP035
    IO as IO,
    TYPE_CHECKING as TYPE_CHECKING,
    AbstractSet as AbstractSet,
    Any as Any,
    AnyStr as AnyStr,
    BinaryIO as BinaryIO,
    Callable as Callable,
    ChainMap as ChainMap,
    ClassVar as ClassVar,
    Counter as Counter,
    DefaultDict as DefaultDict,
    Deque as Deque,
    Dict as Dict,
    ForwardRef as ForwardRef,
    FrozenSet as FrozenSet,
    Generic as Generic,
    List as List,
    NoReturn as NoReturn,
    Optional as Optional,
    Set as Set,
    Text as Text,
    TextIO as TextIO,
    Tuple as Tuple,
    Type as Type,
    TypedDict as TypedDict,
    TypeVar as _TypeVar,
    Union as Union,
    _Alias,
    cast as cast,
    no_type_check as no_type_check,
    no_type_check_decorator as no_type_check_decorator,
    overload as overload,
    type_check_only,
)

if sys.version_info >= (3, 10):
    from types import UnionType

# Please keep order the same as at runtime.
__all__ = [
    # Super-special typing primitives.
    "Any",
    "ClassVar",
    "Concatenate",
    "Final",
    "LiteralString",
    "ParamSpec",
    "ParamSpecArgs",
    "ParamSpecKwargs",
    "Self",
    "Type",
    "TypeVar",
    "TypeVarTuple",
    "Unpack",
    # ABCs (from collections.abc).
    "Awaitable",
    "AsyncIterator",
    "AsyncIterable",
    "Coroutine",
    "AsyncGenerator",
    "AsyncContextManager",
    "Buffer",
    "ChainMap",
    # Concrete collection types.
    "ContextManager",
    "Counter",
    "Deque",
    "DefaultDict",
    "NamedTuple",
    "OrderedDict",
    "TypedDict",
    # Structural checks, a.k.a. protocols.
    "SupportsAbs",
    "SupportsBytes",
    "SupportsComplex",
    "SupportsFloat",
    "SupportsIndex",
    "SupportsInt",
    "SupportsRound",
    "Reader",
    "Writer",
    # One-off things.
    "Annotated",
    "assert_never",
    "assert_type",
    "clear_overloads",
    "dataclass_transform",
    "deprecated",
    "Doc",
    "evaluate_forward_ref",
    "get_overloads",
    "final",
    "Format",
    "get_annotations",
    "get_args",
    "get_origin",
    "get_original_bases",
    "get_protocol_members",
    "get_type_hints",
    "IntVar",
    "is_protocol",
    "is_typeddict",
    "Literal",
    "NewType",
    "overload",
    "override",
    "Protocol",
    "Sentinel",
    "reveal_type",
    "runtime",
    "runtime_checkable",
    "Text",
    "TypeAlias",
    "TypeAliasType",
    "TypeForm",
    "TypeGuard",
    "TypeIs",
    "TYPE_CHECKING",
    "Never",
    "NoReturn",
    "ReadOnly",
    "Required",
    "NotRequired",
    "NoDefault",
    "NoExtraItems",
    # Pure aliases, have always been in typing
    "AbstractSet",
    "AnyStr",
    "BinaryIO",
    "Callable",
    "Collection",
    "Container",
    "Dict",
    "ForwardRef",
    "FrozenSet",
    "Generator",
    "Generic",
    "Hashable",
    "IO",
    "ItemsView",
    "Iterable",
    "Iterator",
    "KeysView",
    "List",
    "Mapping",
    "MappingView",
    "Match",
    "MutableMapping",
    "MutableSequence",
    "MutableSet",
    "Optional",
    "Pattern",
    "Reversible",
    "Sequence",
    "Set",
    "Sized",
    "TextIO",
    "Tuple",
    "Union",
    "ValuesView",
    "cast",
    "no_type_check",
    "no_type_check_decorator",
    # Added dynamically
    "CapsuleType",
]

_T = _TypeVar("_T")
_F = _TypeVar("_F", bound=Callable[..., Any])
_TC = _TypeVar("_TC", bound=type[object])
_T_co = _TypeVar("_T_co", covariant=True)  # Any type covariant containers.
_T_contra = _TypeVar("_T_contra", contravariant=True)

class _Final: ...  # This should be imported from typing but that breaks pytype

# unfortunately we have to duplicate this class definition from typing.pyi or we break pytype
class _SpecialForm(_Final):
    def __getitem__(self, parameters: Any) -> object: ...
    if sys.version_info >= (3, 10):
        def __or__(self, other: Any) -> _SpecialForm: ...
        def __ror__(self, other: Any) -> _SpecialForm: ...

# Do not import (and re-export) Protocol or runtime_checkable from
# typing module because type checkers need to be able to distinguish
# typing.Protocol and typing_extensions.Protocol so they can properly
# warn users about potential runtime exceptions when using typing.Protocol
# on older versions of Python.
Protocol: _SpecialForm

def runtime_checkable(cls: _TC) -> _TC: ...

# This alias for above is kept here for backwards compatibility.
runtime = runtime_checkable
Final: _SpecialForm

def final(f: _F) -> _F: ...

Literal: _SpecialForm

def IntVar(name: str) -> Any: ...  # returns a new TypeVar

# Internal mypy fallback type for all typed dicts (does not exist at runtime)
# N.B. Keep this mostly in sync with typing._TypedDict/mypy_extensions._TypedDict
@type_check_only
class _TypedDict(Mapping[str, object], metaclass=abc.ABCMeta):
    __required_keys__: ClassVar[frozenset[str]]
    __optional_keys__: ClassVar[frozenset[str]]
    __total__: ClassVar[bool]
    __orig_bases__: ClassVar[tuple[Any, ...]]
    # PEP 705
    __readonly_keys__: ClassVar[frozenset[str]]
    __mutable_keys__: ClassVar[frozenset[str]]
    # PEP 728
    __closed__: ClassVar[bool]
    __extra_items__: ClassVar[AnnotationForm]
    def copy(self) -> Self: ...
    # Using Never so that only calls using mypy plugin hook that specialize the signature
    # can go through.
    def setdefault(self, k: Never, default: object) -> object: ...
    # Mypy plugin hook for 'pop' expects that 'default' has a type variable type.
    def pop(self, k: Never, default: _T = ...) -> object: ...  # pyright: ignore[reportInvalidTypeVarUse]
    def update(self, m: Self, /) -> None: ...
    def items(self) -> dict_items[str, object]: ...
    def keys(self) -> dict_keys[str, object]: ...
    def values(self) -> dict_values[str, object]: ...
    def __delitem__(self, k: Never) -> None: ...
    @overload
    def __or__(self, value: Self, /) -> Self: ...
    @overload
    def __or__(self, value: dict[str, Any], /) -> dict[str, object]: ...
    @overload
    def __ror__(self, value: Self, /) -> Self: ...
    @overload
    def __ror__(self, value: dict[str, Any], /) -> dict[str, object]: ...
    # supposedly incompatible definitions of `__ior__` and `__or__`:
    # Since this module defines "Self" it is not recognized by Ruff as typing_extensions.Self
    def __ior__(self, value: Self, /) -> Self: ...  # type: ignore[misc]

OrderedDict = _Alias()

if sys.version_info >= (3, 13):
    from typing import get_type_hints as get_type_hints
else:
    def get_type_hints(
        obj: Any, globalns: dict[str, Any] | None = None, localns: Mapping[str, Any] | None = None, include_extras: bool = False
    ) -> dict[str, AnnotationForm]: ...

def get_args(tp: AnnotationForm) -> tuple[AnnotationForm, ...]: ...

if sys.version_info >= (3, 10):
    @overload
    def get_origin(tp: UnionType) -> type[UnionType]: ...

@overload
def get_origin(tp: GenericAlias) -> type: ...
@overload
def get_origin(tp: ParamSpecArgs | ParamSpecKwargs) -> ParamSpec: ...
@overload
def get_origin(tp: AnnotationForm) -> AnnotationForm | None: ...

Annotated: _SpecialForm
_AnnotatedAlias: Any  # undocumented

# New and changed things in 3.10
if sys.version_info >= (3, 10):
    from typing import (
        Concatenate as Concatenate,
        ParamSpecArgs as ParamSpecArgs,
        ParamSpecKwargs as ParamSpecKwargs,
        TypeAlias as TypeAlias,
        TypeGuard as TypeGuard,
        is_typeddict as is_typeddict,
    )
else:
    @final
    class ParamSpecArgs:
        @property
        def __origin__(self) -> ParamSpec: ...
        def __init__(self, origin: ParamSpec) -> None: ...

    @final
    class ParamSpecKwargs:
        @property
        def __origin__(self) -> ParamSpec: ...
        def __init__(self, origin: ParamSpec) -> None: ...

    Concatenate: _SpecialForm
    TypeAlias: _SpecialForm
    TypeGuard: _SpecialForm
    def is_typeddict(tp: object) -> bool: ...

# New and changed things in 3.11
if sys.version_info >= (3, 11):
    from typing import (
        LiteralString as LiteralString,
        NamedTuple as NamedTuple,
        Never as Never,
        NewType as NewType,
        NotRequired as NotRequired,
        Required as Required,
        Self as Self,
        Unpack as Unpack,
        assert_never as assert_never,
        assert_type as assert_type,
        clear_overloads as clear_overloads,
        dataclass_transform as dataclass_transform,
        get_overloads as get_overloads,
        reveal_type as reveal_type,
    )
else:
    Self: _SpecialForm
    Never: _SpecialForm
    def reveal_type(obj: _T, /) -> _T: ...
    def assert_never(arg: Never, /) -> Never: ...
    def assert_type(val: _T, typ: AnnotationForm, /) -> _T: ...
    def clear_overloads() -> None: ...
    def get_overloads(func: Callable[..., object]) -> Sequence[Callable[..., object]]: ...

    Required: _SpecialForm
    NotRequired: _SpecialForm
    LiteralString: _SpecialForm
    Unpack: _SpecialForm

    def dataclass_transform(
        *,
        eq_default: bool = True,
        order_default: bool = False,
        kw_only_default: bool = False,
        frozen_default: bool = False,
        field_specifiers: tuple[type[Any] | Callable[..., Any], ...] = (),
        **kwargs: object,
    ) -> IdentityFunction: ...

    class NamedTuple(tuple[Any, ...]):
        _field_defaults: ClassVar[dict[str, Any]]
        _fields: ClassVar[tuple[str, ...]]
        __orig_bases__: ClassVar[tuple[Any, ...]]
        @overload
        def __init__(self, typename: str, fields: Iterable[tuple[str, Any]] = ...) -> None: ...
        @overload
        def __init__(self, typename: str, fields: None = None, **kwargs: Any) -> None: ...
        @classmethod
        def _make(cls, iterable: Iterable[Any]) -> Self: ...
        def _asdict(self) -> dict[str, Any]: ...
        def _replace(self, **kwargs: Any) -> Self: ...

    class NewType:
        def __init__(self, name: str, tp: AnnotationForm) -> None: ...
        def __call__(self, obj: _T, /) -> _T: ...
        __supertype__: type | NewType
        if sys.version_info >= (3, 10):
            def __or__(self, other: Any) -> _SpecialForm: ...
            def __ror__(self, other: Any) -> _SpecialForm: ...

if sys.version_info >= (3, 12):
    from collections.abc import Buffer as Buffer
    from types import get_original_bases as get_original_bases
    from typing import (
        SupportsAbs as SupportsAbs,
        SupportsBytes as SupportsBytes,
        SupportsComplex as SupportsComplex,
        SupportsFloat as SupportsFloat,
        SupportsIndex as SupportsIndex,
        SupportsInt as SupportsInt,
        SupportsRound as SupportsRound,
        override as override,
    )
else:
    def override(arg: _F, /) -> _F: ...
    def get_original_bases(cls: type, /) -> tuple[Any, ...]: ...

    # mypy and pyright object to this being both ABC and Protocol.
    # At runtime it inherits from ABC and is not a Protocol, but it is on the
    # allowlist for use as a Protocol.
    @runtime_checkable
    class Buffer(Protocol, abc.ABC):  # type: ignore[misc]  # pyright: ignore[reportGeneralTypeIssues]
        # Not actually a Protocol at runtime; see
        # https://github.com/python/typeshed/issues/10224 for why we're defining it this way
        def __buffer__(self, flags: int, /) -> memoryview: ...

    @runtime_checkable
    class SupportsInt(Protocol, metaclass=abc.ABCMeta):
        @abc.abstractmethod
        def __int__(self) -> int: ...

    @runtime_checkable
    class SupportsFloat(Protocol, metaclass=abc.ABCMeta):
        @abc.abstractmethod
        def __float__(self) -> float: ...

    @runtime_checkable
    class SupportsComplex(Protocol, metaclass=abc.ABCMeta):
        @abc.abstractmethod
        def __complex__(self) -> complex: ...

    @runtime_checkable
    class SupportsBytes(Protocol, metaclass=abc.ABCMeta):
        @abc.abstractmethod
        def __bytes__(self) -> bytes: ...

    @runtime_checkable
    class SupportsIndex(Protocol, metaclass=abc.ABCMeta):
        @abc.abstractmethod
        def __index__(self) -> int: ...

    @runtime_checkable
    class SupportsAbs(Protocol[_T_co]):
        @abc.abstractmethod
        def __abs__(self) -> _T_co: ...

    @runtime_checkable
    class SupportsRound(Protocol[_T_co]):
        @overload
        @abc.abstractmethod
        def __round__(self) -> int: ...
        @overload
        @abc.abstractmethod
        def __round__(self, ndigits: int, /) -> _T_co: ...

if sys.version_info >= (3, 14):
    from io import Reader as Reader, Writer as Writer
else:
    @runtime_checkable
    class Reader(Protocol[_T_co]):
        @abc.abstractmethod
        def read(self, size: int = ..., /) -> _T_co: ...

    @runtime_checkable
    class Writer(Protocol[_T_contra]):
        @abc.abstractmethod
        def write(self, data: _T_contra, /) -> int: ...

if sys.version_info >= (3, 13):
    from types import CapsuleType as CapsuleType
    from typing import (
        NoDefault as NoDefault,
        ParamSpec as ParamSpec,
        ReadOnly as ReadOnly,
        TypeIs as TypeIs,
        TypeVar as TypeVar,
        TypeVarTuple as TypeVarTuple,
        get_protocol_members as get_protocol_members,
        is_protocol as is_protocol,
    )
    from warnings import deprecated as deprecated
else:
    def is_protocol(tp: type, /) -> bool: ...
    def get_protocol_members(tp: type, /) -> frozenset[str]: ...
    @final
    class _NoDefaultType: ...

    NoDefault: _NoDefaultType
    @final
    class CapsuleType: ...

    class deprecated:
        message: LiteralString
        category: type[Warning] | None
        stacklevel: int
        def __init__(self, message: LiteralString, /, *, category: type[Warning] | None = ..., stacklevel: int = 1) -> None: ...
        def __call__(self, arg: _T, /) -> _T: ...

    @final
    class TypeVar:
        @property
        def __name__(self) -> str: ...
        @property
        def __bound__(self) -> AnnotationForm | None: ...
        @property
        def __constraints__(self) -> tuple[AnnotationForm, ...]: ...
        @property
        def __covariant__(self) -> bool: ...
        @property
        def __contravariant__(self) -> bool: ...
        @property
        def __infer_variance__(self) -> bool: ...
        @property
        def __default__(self) -> AnnotationForm: ...
        def __init__(
            self,
            name: str,
            *constraints: AnnotationForm,
            bound: AnnotationForm | None = None,
            covariant: bool = False,
            contravariant: bool = False,
            default: AnnotationForm = ...,
            infer_variance: bool = False,
        ) -> None: ...
        def has_default(self) -> bool: ...
        def __typing_prepare_subst__(self, alias: Any, args: Any) -> tuple[Any, ...]: ...
        if sys.version_info >= (3, 10):
            def __or__(self, right: Any) -> _SpecialForm: ...
            def __ror__(self, left: Any) -> _SpecialForm: ...
        if sys.version_info >= (3, 11):
            def __typing_subst__(self, arg: Any) -> Any: ...

    @final
    class ParamSpec:
        @property
        def __name__(self) -> str: ...
        @property
        def __bound__(self) -> AnnotationForm | None: ...
        @property
        def __covariant__(self) -> bool: ...
        @property
        def __contravariant__(self) -> bool: ...
        @property
        def __infer_variance__(self) -> bool: ...
        @property
        def __default__(self) -> AnnotationForm: ...
        def __init__(
            self,
            name: str,
            *,
            bound: None | AnnotationForm | str = None,
            contravariant: bool = False,
            covariant: bool = False,
            default: AnnotationForm = ...,
        ) -> None: ...
        @property
        def args(self) -> ParamSpecArgs: ...
        @property
        def kwargs(self) -> ParamSpecKwargs: ...
        def has_default(self) -> bool: ...
        def __typing_prepare_subst__(self, alias: Any, args: Any) -> tuple[Any, ...]: ...
        if sys.version_info >= (3, 10):
            def __or__(self, right: Any) -> _SpecialForm: ...
            def __ror__(self, left: Any) -> _SpecialForm: ...

    @final
    class TypeVarTuple:
        @property
        def __name__(self) -> str: ...
        @property
        def __default__(self) -> AnnotationForm: ...
        def __init__(self, name: str, *, default: AnnotationForm = ...) -> None: ...
        def __iter__(self) -> Any: ...  # Unpack[Self]
        def has_default(self) -> bool: ...
        def __typing_prepare_subst__(self, alias: Any, args: Any) -> tuple[Any, ...]: ...

    ReadOnly: _SpecialForm
    TypeIs: _SpecialForm

# TypeAliasType was added in Python 3.12, but had significant changes in 3.14.
if sys.version_info >= (3, 14):
    from typing import TypeAliasType as TypeAliasType
else:
    @final
    class TypeAliasType:
        def __init__(
            self, name: str, value: AnnotationForm, *, type_params: tuple[TypeVar | ParamSpec | TypeVarTuple, ...] = ()
        ) -> None: ...
        @property
        def __value__(self) -> AnnotationForm: ...
        @property
        def __type_params__(self) -> tuple[TypeVar | ParamSpec | TypeVarTuple, ...]: ...
        @property
        # `__parameters__` can include special forms if a `TypeVarTuple` was
        # passed as a `type_params` element to the constructor method.
        def __parameters__(self) -> tuple[TypeVar | ParamSpec | AnnotationForm, ...]: ...
        @property
        def __name__(self) -> str: ...
        # It's writable on types, but not on instances of TypeAliasType.
        @property
        def __module__(self) -> str | None: ...  # type: ignore[override]
        # Returns typing._GenericAlias, which isn't stubbed.
        def __getitem__(self, parameters: Incomplete | tuple[Incomplete, ...]) -> AnnotationForm: ...
        def __init_subclass__(cls, *args: Unused, **kwargs: Unused) -> NoReturn: ...
        if sys.version_info >= (3, 10):
            def __or__(self, right: Any) -> _SpecialForm: ...
            def __ror__(self, left: Any) -> _SpecialForm: ...

# PEP 727
class Doc:
    documentation: str
    def __init__(self, documentation: str, /) -> None: ...
    def __hash__(self) -> int: ...
    def __eq__(self, other: object) -> bool: ...

# PEP 728
class _NoExtraItemsType: ...

NoExtraItems: _NoExtraItemsType

# PEP 747
TypeForm: _SpecialForm

# PEP 649/749
if sys.version_info >= (3, 14):
    from typing import evaluate_forward_ref as evaluate_forward_ref

    from annotationlib import Format as Format, get_annotations as get_annotations
else:
    class Format(enum.IntEnum):
        VALUE = 1
        VALUE_WITH_FAKE_GLOBALS = 2
        FORWARDREF = 3
        STRING = 4

    @overload
    def get_annotations(
        obj: Any,  # any object with __annotations__ or __annotate__
        *,
        globals: Mapping[str, Any] | None = None,  # value types depend on the key
        locals: Mapping[str, Any] | None = None,  # value types depend on the key
        eval_str: bool = False,
        format: Literal[Format.STRING],
    ) -> dict[str, str]: ...
    @overload
    def get_annotations(
        obj: Any,  # any object with __annotations__ or __annotate__
        *,
        globals: Mapping[str, Any] | None = None,  # value types depend on the key
        locals: Mapping[str, Any] | None = None,  # value types depend on the key
        eval_str: bool = False,
        format: Literal[Format.FORWARDREF],
    ) -> dict[str, AnnotationForm | ForwardRef]: ...
    @overload
    def get_annotations(
        obj: Any,  # any object with __annotations__ or __annotate__
        *,
        globals: Mapping[str, Any] | None = None,  # value types depend on the key
        locals: Mapping[str, Any] | None = None,  # value types depend on the key
        eval_str: bool = False,
        format: Format = Format.VALUE,  # noqa: Y011
    ) -> dict[str, AnnotationForm]: ...
    @overload
    def evaluate_forward_ref(
        forward_ref: ForwardRef,
        *,
        owner: Callable[..., object] | type[object] | ModuleType | None = None,  # any callable, class, or module
        globals: Mapping[str, Any] | None = None,  # value types depend on the key
        locals: Mapping[str, Any] | None = None,  # value types depend on the key
        type_params: Iterable[TypeVar | ParamSpec | TypeVarTuple] | None = None,
        format: Literal[Format.STRING],
        _recursive_guard: Container[str] = ...,
    ) -> str: ...
    @overload
    def evaluate_forward_ref(
        forward_ref: ForwardRef,
        *,
        owner: Callable[..., object] | type[object] | ModuleType | None = None,  # any callable, class, or module
        globals: Mapping[str, Any] | None = None,  # value types depend on the key
        locals: Mapping[str, Any] | None = None,  # value types depend on the key
        type_params: Iterable[TypeVar | ParamSpec | TypeVarTuple] | None = None,
        format: Literal[Format.FORWARDREF],
        _recursive_guard: Container[str] = ...,
    ) -> AnnotationForm | ForwardRef: ...
    @overload
    def evaluate_forward_ref(
        forward_ref: ForwardRef,
        *,
        owner: Callable[..., object] | type[object] | ModuleType | None = None,  # any callable, class, or module
        globals: Mapping[str, Any] | None = None,  # value types depend on the key
        locals: Mapping[str, Any] | None = None,  # value types depend on the key
        type_params: Iterable[TypeVar | ParamSpec | TypeVarTuple] | None = None,
        format: Format | None = None,
        _recursive_guard: Container[str] = ...,
    ) -> AnnotationForm: ...

# PEP 661
class Sentinel:
    def __init__(self, name: str, repr: str | None = None) -> None: ...
    if sys.version_info >= (3, 14):
        def __or__(self, other: Any) -> UnionType: ...  # other can be any type form legal for unions
        def __ror__(self, other: Any) -> UnionType: ...  # other can be any type form legal for unions
    else:
        def __or__(self, other: Any) -> _SpecialForm: ...  # other can be any type form legal for unions
        def __ror__(self, other: Any) -> _SpecialForm: ...  # other can be any type form legal for unions
